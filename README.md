# AstrBot 群聊主动对话插件


> ⚠️ **这是插件的初版本，功能相对基础，架构和功能设计都还不够完善。后续将进行大幅度的重构和改进。**

## 📖 简介

这是一个为 AstrBot 设计的群聊主动对话插件，基于原版私聊主动对话插件改造。当群聊长时间没有人发言时，机器人会主动发起对话，活跃群聊氛围。

## ✨ 主要功能 (初版实现)

> 📝 **注意：当前为初版实现，功能相对简单，后续版本将大幅扩展**

### 🎯 核心功能
- **智能群聊监控**: 实时监控群聊活跃度，检测不活跃状态
- **自动主动发言**: 当群聊超过设定时间无人说话时，自动发送主动消息
- **渐进式交互**: 支持连续多次主动发言，每次使用不同的策略和语调
- **时间段适配**: 根据不同时间段（早上、下午、晚上等）调整消息内容

### 📊 群聊特定优化
- **群聊白名单**: 支持设置特定群聊白名单，只在指定群聊中启用
- **活跃度检测**: 避免在群聊正在热烈讨论时发送主动消息
- **群聊上下文**: 针对群聊场景优化的提示词和交互方式
- **消息计数管理**: 每个群聊独立管理连续主动消息次数

### ⚙️ 可配置参数
- **时间设置**: 不活跃时间阈值、发送延迟、活动时间段等
- **消息限制**: 最大连续主动消息数量，避免过度打扰
- **白名单管理**: 灵活的群聊白名单配置
- **节日检测**: 支持节日相关的特殊问候和提示

## ⚙️ 配置参数

### 基础配置
```json
{
  "self_id": "你的机器人QQ号",           // 必填项
  "time_settings": {
    "inactive_time_seconds": 10800,      // 3小时无人说话后触发
    "max_response_delay_seconds": 1800,  // 30分钟内随机发送
    "activity_start_hour": 9,            // 每天9点开始活动
    "activity_end_hour": 22,             // 每天22点停止活动
    "max_consecutive_messages": 2        // 最多连续发送2条消息
  }
}
```

### 群聊白名单
```json
{
  "whitelist": {
    "enabled": true,                     // 启用白名单
    "group_ids": ["12345", "67890"]      // 允许的群聊ID列表
  }
}
```

### 群聊特定设置
```json
{
  "group_specific_settings": {
    "avoid_disturbing_active_chat": true,    // 避免打扰活跃群聊
    "active_threshold_messages": 5,         // 30分钟内超过5条消息认为活跃
    "active_check_minutes": 30,             // 活跃度检测时间窗口
  }
}
```

## 📁 项目结构

```
astrbot_plugin_InitialDialogue_for_group/
├── main.py                           # 插件主文件
├── metadata.yaml                     # 插件元数据
├── _conf_schema.json                # 配置文件模式
├── requirements.txt                  # 依赖列表
├── core/                            # 核心功能模块
│   └── group_initiative_dialogue_core.py
└── utils/                           # 工具模块
    ├── config_manager.py            # 配置管理
    ├── data_loader.py               # 数据加载
    ├── group_manager.py             # 群组管理
    ├── group_message_manager.py     # 消息管理
    └── task_manager.py              # 任务管理
```

## 🚀 快速开始

### 安装要求

- AstrBot 框架
- Python 3.8+
- 配置可用的大语言模型

### 安装步骤

1. **下载插件**: 将整个 `astrbot_plugin_InitialDialogue_for_group` 文件夹放入 AstrBot 的插件目录

2. **配置插件**: 在 AstrBot 管理界面中配置插件参数，至少需要设置：
   - `self_id`: 机器人的QQ号（必填）
   - `whitelist`: 设置允许的群聊ID（如果启用白名单）

3. **启用插件**: 在 AstrBot 插件管理中启用该插件

4. **测试功能**: 在配置的群聊中等待指定时间，观察机器人是否会主动发言

## 🔧 工作原理

### 1. 消息监听
- 监听所有群聊消息事件
- 更新群聊最后活跃时间
- 重置连续主动消息计数

### 2. 不活跃检测
- 每30秒检查一次所有群聊状态
- 计算每个群聊的不活跃时间
- 触发条件：超过设定的不活跃时间阈值

### 3. 智能发送
- 根据连续发送次数选择不同的提示词策略
- 考虑当前时间段调整消息内容
- 避免在群聊活跃期间发送

### 4. 状态管理
- 持久化存储群聊状态数据
- 管理每个群聊的连续消息计数
- 记录主动消息发送历史

## 🚧 已知限制（初版）

> ⚠️ **当前版本存在以下限制，将在后续版本中改进**

### 功能限制
- 配置选项相对简单，缺少高级自定义功能
- 消息生成逻辑较为基础，上下文理解有限
- 缺少智能的群聊氛围分析功能
- 群组管理功能不够完善
- 错误处理和异常恢复机制简单

### 技术限制
- 依赖 AstrBot 框架的特定版本
- 需要配置可用的大语言模型
- 机器人需要在目标群聊中有发言权限
- 性能优化空间较大

## 🔮 后续版本规划

> 📋 **重大更新计划**：后续版本将进行完整的架构重构

### 2.0 版本规划（重构版本）
- 🧠 **智能上下文分析**：深度理解群聊历史和成员互动模式
- 🎨 **丰富的交互形式**：支持图片、表情、多媒体内容
- 📈 **高级群组分析**：成员活跃度分析、话题偏好检测
- ⚡ **性能优化**：异步处理优化、内存使用优化
- 🛡️ **完善的错误处理**：robust的异常处理和自动恢复
- 🎯 **个性化策略**：基于群聊特征的自适应对话策略

### 长期发展方向
- 🤖 **多平台支持**：支持更多聊天平台
- 📊 **数据分析**：群聊活跃度统计和可视化面板
- 🔌 **插件生态**：支持第三方扩展和自定义模块
- 🎭 **高级 AI 能力**：情感分析、话题检测、智能回复

## 📋 提示词策略

插件采用渐进式提示词策略：

- **首次发送** (1次): 友好地表达注意到群聊冷清，想要活跃气氛
- **继续发送** (2次): 理解大家可能在忙，但仍然关心群友
- **最后发送** (达到上限): 温和地表达会减少主动发言频率

## ⚠️ 注意事项

### 使用建议
1. **合理设置时间**: 建议不活跃时间设置为2-4小时，避免过于频繁
2. **限制群聊数量**: 不要在过多群聊中同时启用，可能造成消息轰炸
3. **观察反馈**: 根据群友反馈及时调整配置参数
4. **先行测试**: 在小群或测试群中先测试功能效果
5. **获得同意**: 确保在使用前获得群成员的理解和同意

### 注意事项
1. **初版限制**: 当前版本功能较为基础，可能存在未知问题
2. **性能影响**: 大量群聊可能影响性能，建议逐步测试
3. **权限要求**: 确保机器人在目标群聊中有正常的发言权限
4. **LLM 依赖**: 功能完全依赖于已配置的大语言模型

## 🐛 问题反馈

如果您在使用过程中遇到任何问题或有改进建议，请：

1. 查看插件日志文件获取详细错误信息
2. 检查配置参数是否正确设置
3. 创建详细的问题反馈，包含：
   - 使用环境（AstrBot版本、Python版本等）
   - 配置参数
   - 错误日志
   - 复现步骤

## 📄 版本信息

- **版本**: v1.0.0 (初版)
- **作者**: Jason
- **基于**: InitiativeDialogue 私聊版本
- **适用**: AstrBot 框架
- **状态**: 🚧 初版实现，后续将大幅改进

## 📝 更新日志

### v1.0.0 (2024-初版)
- 🎉 首个群聊版本发布
- 🔄 基于私聊版本完全重构
- ✨ 新增群聊特定功能和优化
- 🎯 支持群聊白名单和活跃度检测
- ⚠️ 初版实现，功能相对基础

## 🤝 贡献指南

欢迎为插件的改进做出贡献！在提交 Pull Request 之前请确保：

- 代码符合项目的编码规范
- 添加了必要的注释和文档
- 通过了基本的功能测试
- 考虑了向后兼容性

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- 感谢 [AstrBot](https://github.com/Soulter/AstrBot) 框架的支持
- 感谢原版 InitiativeDialogue 插件的设计思路
- 感谢所有测试用户的反馈和建议

---

**⚠️ 再次提醒：这是插件的初版实现，功能和架构都还需要大幅改进。我们正在积极开发更完善和稳定的版本，敬请期待后续的重大更新！**
